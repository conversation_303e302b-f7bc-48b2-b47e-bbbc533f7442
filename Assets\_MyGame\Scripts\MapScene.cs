using UnityEngine;
using UnityEngine.Tilemaps;

public class MapScene : MonoBehaviour
{
    public GameObject mapGo;

    [Header("拖动设置")]
    public float dragSpeed = 1f;

    [Header("缩放设置")]
    public float zoomSpeed = 5f;
    public float minZoom = 10f;
    private float maxZoom;

    [Header("地图边界设置")]
    public int mapWidth = 4096;
    public int mapHeight = 4096;
    public Vector2 mapCenter = Vector2.zero;

    private Camera mainCamera;
    private bool isDragging = false;
    private Vector3 lastMousePosition;

    // 触摸缩放相关变量
    private bool isTouchZooming = false;
    private float lastTouchDistance = 0f;

    void Start()
    {
        InitializeComponents();
    }

    void Update()
    {
        HandleInput();
    }

    void InitializeComponents()
    {
        mainCamera = Camera.main;
        if (mainCamera == null)
            mainCamera = FindObjectOfType<Camera>();

        maxZoom = mainCamera.orthographicSize;
    }

    void HandleInput()
    {
        HandleZoom();
        HandleTouchInput();

        if (Input.GetMouseButtonDown(0))
        {
            StartDrag();
        }
        else if (Input.GetMouseButton(0) && isDragging)
        {
            UpdateDrag();
        }
        else if (Input.GetMouseButtonUp(0))
        {
            EndDrag();
        }
    }

    void StartDrag()
    {
        isDragging = true;
        lastMousePosition = Input.mousePosition;
    }

    void UpdateDrag()
    {
        Vector3 currentMousePosition = Input.mousePosition;
        Vector3 mouseDelta = lastMousePosition - currentMousePosition;

        // 将屏幕坐标差值转换为世界坐标差值
        Vector3 worldDelta = mainCamera.ScreenToWorldPoint(mouseDelta) - mainCamera.ScreenToWorldPoint(Vector3.zero);
        Debug.Log($"worldDelta: {worldDelta}");

        // 只在X和Y轴移动，保持Z坐标不变
        Vector3 newPosition = mainCamera.transform.position + new Vector3(worldDelta.x * dragSpeed, worldDelta.y * dragSpeed, 0);

        newPosition = ClampCameraPosition(newPosition);

        mainCamera.transform.position = newPosition;
        lastMousePosition = currentMousePosition;
    }

    void EndDrag()
    {
        isDragging = false;
    }

    void HandleZoom()
    {
        float scroll = Input.GetAxis("Mouse ScrollWheel");
        if (scroll != 0f)
        {
            float currentSize = mainCamera.orthographicSize;
            float newSize = currentSize - scroll * zoomSpeed;
            newSize = Mathf.Clamp(newSize, minZoom, maxZoom);

            mainCamera.orthographicSize = newSize;

            // 缩放后重新限制相机位置
            Vector3 clampedPosition = ClampCameraPosition(mainCamera.transform.position);
            mainCamera.transform.position = clampedPosition;
        }
    }

    void HandleTouchInput()
    {
        if (Input.touchCount == 2)
        {
            Touch touch1 = Input.GetTouch(0);
            Touch touch2 = Input.GetTouch(1);

            float currentTouchDistance = Vector2.Distance(touch1.position, touch2.position);

            if (!isTouchZooming)
            {
                isTouchZooming = true;
                lastTouchDistance = currentTouchDistance;
                isDragging = false; // 停止拖拽
            }
            else
            {
                float deltaDistance = currentTouchDistance - lastTouchDistance;
                float zoomFactor = deltaDistance * 0.01f; // 调整缩放敏感度

                float currentSize = mainCamera.orthographicSize;
                float newSize = currentSize - zoomFactor * zoomSpeed;
                newSize = Mathf.Clamp(newSize, minZoom, maxZoom);

                mainCamera.orthographicSize = newSize;

                // 缩放后重新限制相机位置
                Vector3 clampedPosition = ClampCameraPosition(mainCamera.transform.position);
                mainCamera.transform.position = clampedPosition;

                lastTouchDistance = currentTouchDistance;
            }
        }
        else
        {
            isTouchZooming = false;
        }
    }

    Vector3 ClampCameraPosition(Vector3 targetPosition)
    {
        if (mainCamera == null) return targetPosition;

        float cameraHeight = mainCamera.orthographicSize * 2f;
        float cameraWidth = cameraHeight * mainCamera.aspect;

        // 使用手动设置的地图边界
        float minX = mapCenter.x - mapWidth * 0.005f + cameraWidth * 0.5f;
        float maxX = mapCenter.x + mapWidth * 0.005f - cameraWidth * 0.5f;
        float minY = mapCenter.y - mapHeight * 0.005f + cameraHeight * 0.5f;
        float maxY = mapCenter.y + mapHeight * 0.005f - cameraHeight * 0.5f;

        // 如果地图比相机视野小，固定在中心
        if (maxX < minX)
        {
            minX = maxX = mapCenter.x;
        }

        if (maxY < minY)
        {
            minY = maxY = mapCenter.y;
        }

        targetPosition.x = Mathf.Clamp(targetPosition.x, minX, maxX);
        targetPosition.y = Mathf.Clamp(targetPosition.y, minY, maxY);

        return targetPosition;
    }
}