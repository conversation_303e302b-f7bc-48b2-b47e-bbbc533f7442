using UnityEngine;
using UnityEngine.Tilemaps;

public class MapScene : MonoBehaviour
{
    public GameObject mapGo;

    [Header("拖动设置")]
    public float dragSpeed = 1f;

    [Header("地图边界设置")]
    public int mapWidth = 2048;
    public int mapHeight = 2048;
    public Vector2 mapCenter = Vector2.zero;

    private Camera mainCamera;
    private bool isDragging = false;
    private Vector3 lastMousePosition;

    void Start()
    {
        InitializeComponents();
    }

    void Update()
    {
        HandleInput();
    }

    void InitializeComponents()
    {
        mainCamera = Camera.main;
        if (mainCamera == null)
            mainCamera = FindObjectOfType<Camera>();
    }

    void HandleInput()
    {
        if (Input.GetMouseButtonDown(0))
        {
            StartDrag();
        }
        else if (Input.GetMouseButton(0) && isDragging)
        {
            UpdateDrag();
        }
        else if (Input.GetMouseButtonUp(0))
        {
            EndDrag();
        }
    }

    void StartDrag()
    {
        isDragging = true;
        lastMousePosition = Input.mousePosition;
    }

    void UpdateDrag()
    {
        Vector3 currentMousePosition = Input.mousePosition;
        Vector3 mouseDelta = lastMousePosition - currentMousePosition;

        // 将屏幕坐标差值转换为世界坐标差值
        Vector3 worldDelta = mainCamera.ScreenToWorldPoint(mouseDelta) - mainCamera.ScreenToWorldPoint(Vector3.zero);
        Debug.Log($"worldDelta: {worldDelta}");

        // 只在X和Y轴移动，保持Z坐标不变
        Vector3 newPosition = mainCamera.transform.position + new Vector3(worldDelta.x * dragSpeed, worldDelta.y * dragSpeed, 0);

        newPosition = ClampCameraPosition(newPosition);

        mainCamera.transform.position = newPosition;
        lastMousePosition = currentMousePosition;
    }

    void EndDrag()
    {
        isDragging = false;
    }

    Vector3 ClampCameraPosition(Vector3 targetPosition)
    {
        if (mainCamera == null) return targetPosition;

        float cameraHeight = mainCamera.orthographicSize * 2f;
        float cameraWidth = cameraHeight * mainCamera.aspect;

        // 使用手动设置的地图边界
        float minX = mapCenter.x - mapWidth * 0.005f + cameraWidth * 0.5f;
        float maxX = mapCenter.x + mapWidth * 0.005f - cameraWidth * 0.5f;
        float minY = mapCenter.y - mapHeight * 0.005f + cameraHeight * 0.5f;
        float maxY = mapCenter.y + mapHeight * 0.005f - cameraHeight * 0.5f;

        // 如果地图比相机视野小，固定在中心
        if (maxX < minX)
        {
            minX = maxX = mapCenter.x;
        }

        if (maxY < minY)
        {
            minY = maxY = mapCenter.y;
        }

        targetPosition.x = Mathf.Clamp(targetPosition.x, minX, maxX);
        targetPosition.y = Mathf.Clamp(targetPosition.y, minY, maxY);

        return targetPosition;
    }
}