using UnityEngine;
using UnityEngine.Tilemaps;
using System;
using System.Collections;

public class MapScene : MonoBehaviour
{
    public GameObject mapGo;
    public GameObject flyTarget;

    public static event Action<Tilemap, Vector3Int, TileBase> OnTileClickedEvent;

    [Header("拖动设置")]
    public float dragSpeed = 1f;

    [Header("缩放设置")]
    public float zoomSpeed = 5f;
    public float minZoom = 10f;
    private float maxZoom;

    [Header("地图边界设置")]
    public int mapWidth = 4096;
    public int mapHeight = 4096;
    public Vector2 mapCenter = Vector2.zero;

    [Header("飞行动画设置")]
    public float flyDuration = 1f;
    public AnimationCurve flyCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);
    public bool removeTileOnClick = true;
    public AudioClip tileClickSound;

    private Camera mainCamera;
    private bool isDragging = false;
    private Vector3 lastMousePosition;

    // 触摸缩放相关变量
    private bool isTouchZooming = false;
    private float lastTouchDistance = 0f;

    void Start()
    {
        InitializeComponents();
    }

    void Update()
    {
        HandleInput();
    }

    void InitializeComponents()
    {
        mainCamera = Camera.main;
        if (mainCamera == null)
            mainCamera = FindObjectOfType<Camera>();

        maxZoom = mainCamera.orthographicSize;
    }

    void HandleInput()
    {
        HandleZoom();
        HandleTouchInput();

        if (Input.GetMouseButtonDown(0))
        {
            if (!isTouchZooming)
            {
                HandleClick();
                StartDrag();
            }
        }
        else if (Input.GetMouseButton(0) && isDragging)
        {
            UpdateDrag();
        }
        else if (Input.GetMouseButtonUp(0))
        {
            EndDrag();
        }
    }

    void StartDrag()
    {
        isDragging = true;
        lastMousePosition = Input.mousePosition;
    }

    void UpdateDrag()
    {
        Vector3 currentMousePosition = Input.mousePosition;
        Vector3 mouseDelta = lastMousePosition - currentMousePosition;

        // 将屏幕坐标差值转换为世界坐标差值
        Vector3 worldDelta = mainCamera.ScreenToWorldPoint(mouseDelta) - mainCamera.ScreenToWorldPoint(Vector3.zero);

        // 只在X和Y轴移动，保持Z坐标不变
        Vector3 newPosition = mainCamera.transform.position + new Vector3(worldDelta.x * dragSpeed, worldDelta.y * dragSpeed, 0);

        newPosition = ClampCameraPosition(newPosition);

        mainCamera.transform.position = newPosition;
        lastMousePosition = currentMousePosition;
    }

    void EndDrag()
    {
        isDragging = false;
    }

    void HandleZoom()
    {
        float scroll = Input.GetAxis("Mouse ScrollWheel");
        if (scroll != 0f)
        {
            float currentSize = mainCamera.orthographicSize;
            float newSize = currentSize - scroll * zoomSpeed;
            newSize = Mathf.Clamp(newSize, minZoom, maxZoom);

            mainCamera.orthographicSize = newSize;

            // 缩放后重新限制相机位置
            Vector3 clampedPosition = ClampCameraPosition(mainCamera.transform.position);
            mainCamera.transform.position = clampedPosition;
        }
    }

    void HandleTouchInput()
    {
        if (Input.touchCount == 2)
        {
            Touch touch1 = Input.GetTouch(0);
            Touch touch2 = Input.GetTouch(1);

            float currentTouchDistance = Vector2.Distance(touch1.position, touch2.position);

            if (!isTouchZooming)
            {
                isTouchZooming = true;
                lastTouchDistance = currentTouchDistance;
                isDragging = false; // 停止拖拽
            }
            else
            {
                float deltaDistance = currentTouchDistance - lastTouchDistance;
                float zoomFactor = deltaDistance * 0.01f; // 调整缩放敏感度

                float currentSize = mainCamera.orthographicSize;
                float newSize = currentSize - zoomFactor * zoomSpeed;
                newSize = Mathf.Clamp(newSize, minZoom, maxZoom);

                mainCamera.orthographicSize = newSize;

                // 缩放后重新限制相机位置
                Vector3 clampedPosition = ClampCameraPosition(mainCamera.transform.position);
                mainCamera.transform.position = clampedPosition;

                lastTouchDistance = currentTouchDistance;
            }
        }
        else
        {
            isTouchZooming = false;
        }
    }

    void HandleClick()
    {
        Vector3 mouseWorldPos = mainCamera.ScreenToWorldPoint(Input.mousePosition);
        mouseWorldPos.z = 0f;

        RaycastHit2D hit = Physics2D.Raycast(mouseWorldPos, Vector2.zero);

        if (hit.collider != null)
        {
            if (hit.collider.TryGetComponent<TilemapCollider2D>(out TilemapCollider2D tilemapCollider))
            {
                Debug.Log("=============" + tilemapCollider.gameObject.name);
                if (tilemapCollider.TryGetComponent<Tilemap>(out Tilemap tilemap))
                {
                    Vector3Int cellPosition = tilemap.WorldToCell(mouseWorldPos);
                    TileBase tile = tilemap.GetTile(cellPosition);

                    if (tile != null)
                    {
                        OnTileClicked(tilemap, cellPosition, tile);

                        // 根据设置决定是否移除瓦片
                        if (removeTileOnClick)
                        {
                            tilemap.SetTile(cellPosition, null);
                        }
                    }
                }
            }
        }
    }

    void OnTileClicked(Tilemap tilemap, Vector3Int cellPosition, TileBase tile)
    {
        Debug.Log($"点击了瓦片: {tile.name} 在位置: {cellPosition} 在Tilemap: {tilemap.name}");
        OnTileClickedEvent?.Invoke(tilemap, cellPosition, tile);

        if (flyTarget != null)
        {
            StartFlyAnimation(tilemap.gameObject, cellPosition);
        }
    }

    void StartFlyAnimation(GameObject tilemapObject, Vector3Int cellPosition)
    {
        // 创建一个临时的飞行对象来表示被点击的瓦片
        GameObject flyObject = new GameObject("FlyingTile");

        // 添加SpriteRenderer来显示瓦片
        SpriteRenderer spriteRenderer = flyObject.AddComponent<SpriteRenderer>();

        // 获取瓦片的精灵
        Tilemap tilemap = tilemapObject.GetComponent<Tilemap>();
        TileBase tile = tilemap.GetTile(cellPosition);

        if (tile is Tile tileAsset)
        {
            spriteRenderer.sprite = tileAsset.sprite;
        }

        // 设置起始位置为瓦片的世界坐标
        Vector3 startPos = tilemap.CellToWorld(cellPosition) + tilemap.cellSize * 0.5f;
        flyObject.transform.position = startPos;

        // 开始飞行协程
        StartCoroutine(FlyToTarget(flyObject, spriteRenderer, startPos, flyTarget.transform.position));
    }

    IEnumerator FlyToTarget(GameObject flyObject, SpriteRenderer spriteRenderer, Vector3 startPos, Vector3 targetPos)
    {
        float elapsedTime = 0f;
        Vector3 initialScale = flyObject.transform.localScale;

        while (elapsedTime < flyDuration)
        {
            elapsedTime += Time.deltaTime;
            float progress = elapsedTime / flyDuration;
            float curveValue = flyCurve.Evaluate(progress);

            // 线性插值位置
            Vector3 currentPos = Vector3.Lerp(startPos, targetPos, curveValue);

            // 添加抛物线效果
            float height = Mathf.Sin(progress * Mathf.PI) * 2f;
            currentPos.y += height;

            flyObject.transform.position = currentPos;

            // 添加旋转效果
            flyObject.transform.Rotate(0, 0, 360f * Time.deltaTime);

            // 添加缩放效果，飞行过程中逐渐变小
            float scale = Mathf.Lerp(1f, 0.3f, progress);
            flyObject.transform.localScale = initialScale * scale;

            // 添加透明度变化
            if (spriteRenderer != null)
            {
                Color color = spriteRenderer.color;
                color.a = Mathf.Lerp(1f, 0.5f, progress);
                spriteRenderer.color = color;
            }

            yield return null;
        }

        // 动画结束后销毁飞行对象
        Destroy(flyObject);
    }

    Vector3 ClampCameraPosition(Vector3 targetPosition)
    {
        if (mainCamera == null) return targetPosition;

        float cameraHeight = mainCamera.orthographicSize * 2f;
        float cameraWidth = cameraHeight * mainCamera.aspect;

        // 使用手动设置的地图边界
        float minX = mapCenter.x - mapWidth * 0.005f + cameraWidth * 0.5f;
        float maxX = mapCenter.x + mapWidth * 0.005f - cameraWidth * 0.5f;
        float minY = mapCenter.y - mapHeight * 0.005f + cameraHeight * 0.5f;
        float maxY = mapCenter.y + mapHeight * 0.005f - cameraHeight * 0.5f;

        // 如果地图比相机视野小，固定在中心
        if (maxX < minX)
        {
            minX = maxX = mapCenter.x;
        }

        if (maxY < minY)
        {
            minY = maxY = mapCenter.y;
        }

        targetPosition.x = Mathf.Clamp(targetPosition.x, minX, maxX);
        targetPosition.y = Mathf.Clamp(targetPosition.y, minY, maxY);

        return targetPosition;
    }
}