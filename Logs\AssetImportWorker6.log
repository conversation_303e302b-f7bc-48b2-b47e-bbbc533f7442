Using pre-set license
Built from '2021.3/staging' branch; Version is '2021.3.14f1 (eee1884e7226) revision 15655304'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit CoreCountrySpecific' Language: 'zh' Physical Memory: 32577 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
D:\Program Files\Unity\2021.3.14f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker6
-projectPath
E:/ProjectsUnity/City
-logFile
Logs/AssetImportWorker6.log
-srvPort
61627
Successfully changed project path to: E:/ProjectsUnity/City
E:/ProjectsUnity/City
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Refreshing native plugins compatible for Editor in 69.61 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.14f1 (eee1884e7226)
[Subsystems] Discovering subsystems at path D:/Program Files/Unity/2021.3.14f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/ProjectsUnity/City/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4060 Ti (ID=0x2803)
    Vendor:   NVIDIA
    VRAM:     7949 MB
    Driver:   32.0.15.6117
Initialize mono
Mono path[0] = 'D:/Program Files/Unity/2021.3.14f1/Editor/Data/Managed'
Mono path[1] = 'D:/Program Files/Unity/2021.3.14f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/Program Files/Unity/2021.3.14f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56568
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/Program Files/Unity/2021.3.14f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: D:/Program Files/Unity/2021.3.14f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: D:/Program Files/Unity/2021.3.14f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.001200 seconds.
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 66.91 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.394 seconds
Domain Reload Profiling:
	ReloadAssembly (394ms)
		BeginReloadAssembly (39ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (0ms)
		EndReloadAssembly (299ms)
			LoadAssemblies (39ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (61ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (17ms)
			SetupLoadedEditorAssemblies (191ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (33ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (67ms)
				BeforeProcessingInitializeOnLoad (1ms)
				ProcessInitializeOnLoadAttributes (61ms)
				ProcessInitializeOnLoadMethodAttributes (29ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.002457 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 69.91 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.903 seconds
Domain Reload Profiling:
	ReloadAssembly (904ms)
		BeginReloadAssembly (162ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (114ms)
		EndReloadAssembly (684ms)
			LoadAssemblies (47ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (174ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (46ms)
			SetupLoadedEditorAssemblies (383ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (8ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (70ms)
				BeforeProcessingInitializeOnLoad (69ms)
				ProcessInitializeOnLoadAttributes (170ms)
				ProcessInitializeOnLoadMethodAttributes (64ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (4ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.03 seconds
Refreshing native plugins compatible for Editor in 0.62 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5726 Unused Serialized files (Serialized files now loaded: 0)
Unloading 58 unused Assets / (1.2 MB). Loaded Objects now: 6167.
Memory consumption went from 221.1 MB to 219.9 MB.
Total: 2.490800 ms (FindLiveObjects: 0.292900 ms CreateObjectMapping: 0.086800 ms MarkObjects: 1.816100 ms  DeleteObjects: 0.294500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.001858 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.64 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.662 seconds
Domain Reload Profiling:
	ReloadAssembly (663ms)
		BeginReloadAssembly (76ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (24ms)
		EndReloadAssembly (533ms)
			LoadAssemblies (47ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (158ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (30ms)
			SetupLoadedEditorAssemblies (262ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (53ms)
				ProcessInitializeOnLoadAttributes (139ms)
				ProcessInitializeOnLoadMethodAttributes (55ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (7ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.81 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5608 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6170.
Memory consumption went from 213.6 MB to 212.4 MB.
Total: 3.315500 ms (FindLiveObjects: 0.284000 ms CreateObjectMapping: 0.181700 ms MarkObjects: 2.409200 ms  DeleteObjects: 0.439600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 3834.631710 seconds.
  path: Assets/Scenes/SampleScene.unity
  artifactKey: Guid(8c9cfa26abfee488c85f1582747f6a02) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes/SampleScene.unity using Guid(8c9cfa26abfee488c85f1582747f6a02) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a6718df236fc125b8f274e35db3f53d8') in 0.005454 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.001834 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.60 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.636 seconds
Domain Reload Profiling:
	ReloadAssembly (636ms)
		BeginReloadAssembly (71ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (24ms)
		EndReloadAssembly (514ms)
			LoadAssemblies (44ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (145ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (29ms)
			SetupLoadedEditorAssemblies (255ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (47ms)
				ProcessInitializeOnLoadAttributes (132ms)
				ProcessInitializeOnLoadMethodAttributes (62ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (8ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.29 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5608 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6173.
Memory consumption went from 213.7 MB to 212.4 MB.
Total: 2.314300 ms (FindLiveObjects: 0.217500 ms CreateObjectMapping: 0.081700 ms MarkObjects: 1.682400 ms  DeleteObjects: 0.332200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 95.502134 seconds.
  path: Assets/_MyGame/RawRes
  artifactKey: Guid(ae974169982d4e540b5b37aea99efce4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/RawRes using Guid(ae974169982d4e540b5b37aea99efce4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a051eb5b57f12ba86b337dcf869d3e1c') in 0.004994 seconds 
========================================================================
Received Import Request.
  Time since last request: 20.715183 seconds.
  path: Assets/_MyGame/RawRes/animal_alice_dodo_V2.fbx
  artifactKey: Guid(643dfb718b481ba4eb0744fbe254db3f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/RawRes/animal_alice_dodo_V2.fbx using Guid(643dfb718b481ba4eb0744fbe254db3f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '1e7208b26047cd032dc34f6eed781bad') in 0.053536 seconds 
========================================================================
Received Import Request.
  Time since last request: 42.246875 seconds.
  path: Assets/_MyGame/RawRes/animal_alice_dodo_V2.png
  artifactKey: Guid(49cf613ee287dcd428dd9252589a0fe6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/RawRes/animal_alice_dodo_V2.png using Guid(49cf613ee287dcd428dd9252589a0fe6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'dde7a16e7809a63660c626007f0061b7') in 0.038031 seconds 
========================================================================
Received Import Request.
  Time since last request: 429.236235 seconds.
  path: Assets/_MyGame/RawRes/animal_alice_dodo_V2.png
  artifactKey: Guid(49cf613ee287dcd428dd9252589a0fe6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/RawRes/animal_alice_dodo_V2.png using Guid(49cf613ee287dcd428dd9252589a0fe6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '32b5711e6da7974c77fc24165d85103f') in 0.004207 seconds 
========================================================================
Received Import Request.
  Time since last request: 6.892231 seconds.
  path: Assets/_MyGame/RawRes/animal_alice_dodo_V2.png
  artifactKey: Guid(49cf613ee287dcd428dd9252589a0fe6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/RawRes/animal_alice_dodo_V2.png using Guid(49cf613ee287dcd428dd9252589a0fe6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'cb0862329e92559ec8a1b4e7909179df') in 0.014524 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.001904 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.61 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.642 seconds
Domain Reload Profiling:
	ReloadAssembly (643ms)
		BeginReloadAssembly (74ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (25ms)
		EndReloadAssembly (517ms)
			LoadAssemblies (43ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (144ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (29ms)
			SetupLoadedEditorAssemblies (263ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (48ms)
				ProcessInitializeOnLoadAttributes (145ms)
				ProcessInitializeOnLoadMethodAttributes (57ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (8ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.23 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5608 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6197.
Memory consumption went from 217.8 MB to 216.6 MB.
Total: 3.067900 ms (FindLiveObjects: 0.228400 ms CreateObjectMapping: 0.072200 ms MarkObjects: 2.471900 ms  DeleteObjects: 0.294800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.001826 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.61 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.646 seconds
Domain Reload Profiling:
	ReloadAssembly (646ms)
		BeginReloadAssembly (75ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (24ms)
		EndReloadAssembly (523ms)
			LoadAssemblies (44ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (151ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (32ms)
			SetupLoadedEditorAssemblies (261ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (51ms)
				ProcessInitializeOnLoadAttributes (143ms)
				ProcessInitializeOnLoadMethodAttributes (53ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (8ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.29 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5608 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6200.
Memory consumption went from 217.8 MB to 216.6 MB.
Total: 2.318300 ms (FindLiveObjects: 0.214800 ms CreateObjectMapping: 0.086700 ms MarkObjects: 1.694400 ms  DeleteObjects: 0.321900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 346.298259 seconds.
  path: Assets/_MyGame/RawRes/New Animator Controller.controller
  artifactKey: Guid(b3441a12525d0cb4ea7e7945e8c4c83d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/RawRes/New Animator Controller.controller using Guid(b3441a12525d0cb4ea7e7945e8c4c83d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '18f5867fa136ad938960d7c56f521428') in 0.017266 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.001816 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.67 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.652 seconds
Domain Reload Profiling:
	ReloadAssembly (652ms)
		BeginReloadAssembly (78ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (24ms)
		EndReloadAssembly (519ms)
			LoadAssemblies (47ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (144ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (29ms)
			SetupLoadedEditorAssemblies (267ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (48ms)
				ProcessInitializeOnLoadAttributes (151ms)
				ProcessInitializeOnLoadMethodAttributes (54ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (7ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.30 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5608 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6203.
Memory consumption went from 217.8 MB to 216.6 MB.
Total: 2.373900 ms (FindLiveObjects: 0.228700 ms CreateObjectMapping: 0.088300 ms MarkObjects: 1.728700 ms  DeleteObjects: 0.327600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.001824 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.60 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.641 seconds
Domain Reload Profiling:
	ReloadAssembly (641ms)
		BeginReloadAssembly (72ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (23ms)
		EndReloadAssembly (519ms)
			LoadAssemblies (45ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (141ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (31ms)
			SetupLoadedEditorAssemblies (268ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (51ms)
				ProcessInitializeOnLoadAttributes (149ms)
				ProcessInitializeOnLoadMethodAttributes (55ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (6ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.22 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5608 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6206.
Memory consumption went from 217.7 MB to 216.5 MB.
Total: 2.457500 ms (FindLiveObjects: 0.219800 ms CreateObjectMapping: 0.090100 ms MarkObjects: 1.788700 ms  DeleteObjects: 0.358300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.001878 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.61 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.660 seconds
Domain Reload Profiling:
	ReloadAssembly (661ms)
		BeginReloadAssembly (71ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (22ms)
		EndReloadAssembly (536ms)
			LoadAssemblies (45ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (149ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (29ms)
			SetupLoadedEditorAssemblies (272ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (52ms)
				ProcessInitializeOnLoadAttributes (154ms)
				ProcessInitializeOnLoadMethodAttributes (52ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (7ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.22 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5608 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6209.
Memory consumption went from 217.7 MB to 216.5 MB.
Total: 2.300100 ms (FindLiveObjects: 0.210000 ms CreateObjectMapping: 0.089100 ms MarkObjects: 1.689200 ms  DeleteObjects: 0.311100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 12151.754853 seconds.
  path: Assets/_MyGame/RawRes/Map
  artifactKey: Guid(4f6654c530ca2c94597575a77f87bc91) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/RawRes/Map using Guid(4f6654c530ca2c94597575a77f87bc91) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '543bbed78e5e350e9afca2110094c6c4') in 0.009445 seconds 
========================================================================
Received Import Request.
  Time since last request: 4.844823 seconds.
  path: Assets/_MyGame/RawRes/Map/11
  artifactKey: Guid(5caaceec87701f24cb1ef08836366950) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/RawRes/Map/11 using Guid(5caaceec87701f24cb1ef08836366950) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ca95af9f2787c99133c086ec5ba2c851') in 0.001297 seconds 
========================================================================
Received Import Request.
  Time since last request: 5.835690 seconds.
  path: Assets/_MyGame/RawRes/Map/11/1.png
  artifactKey: Guid(e9bb19c17de47a1459ba72efa17c9a7c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/RawRes/Map/11/1.png using Guid(e9bb19c17de47a1459ba72efa17c9a7c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ebcf59ea3edff1b6bdc17e8efdbf9670') in 0.222413 seconds 
========================================================================
Received Import Request.
  Time since last request: 13.463742 seconds.
  path: Assets/_MyGame/Bundles
  artifactKey: Guid(0556868a748443e4c88c700a9caacf06) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Bundles using Guid(0556868a748443e4c88c700a9caacf06) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b9d5d4968c616e0bab7aba6cd0a9adc8') in 0.000827 seconds 
========================================================================
Received Import Request.
  Time since last request: 4.991508 seconds.
  path: Assets/_MyGame/Bundles/Map
  artifactKey: Guid(61f9939946768234bb6a5433b9622a6c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Bundles/Map using Guid(61f9939946768234bb6a5433b9622a6c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '78eb25dc0d93e1a08bd62dc0673cad41') in 0.000658 seconds 
========================================================================
Received Import Request.
  Time since last request: 21.251126 seconds.
  path: Assets/_MyGame/Bundles/Map/map11.prefab
  artifactKey: Guid(5b753a5a20778624e9b05a67e11588c5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Bundles/Map/map11.prefab using Guid(5b753a5a20778624e9b05a67e11588c5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ee83c06d03521a5d4f09210cd57fed5c') in 0.122870 seconds 
========================================================================
Received Import Request.
  Time since last request: 44.646123 seconds.
  path: Assets/_MyGame/Bundles/Map/map11.prefab
  artifactKey: Guid(5b753a5a20778624e9b05a67e11588c5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Bundles/Map/map11.prefab using Guid(5b753a5a20778624e9b05a67e11588c5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '7b0aa61463c0131cb3c5721ec0745f02') in 0.006488 seconds 
========================================================================
Received Import Request.
  Time since last request: 15.865667 seconds.
  path: Assets/_MyGame/Bundles/Map/map11.prefab
  artifactKey: Guid(5b753a5a20778624e9b05a67e11588c5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Bundles/Map/map11.prefab using Guid(5b753a5a20778624e9b05a67e11588c5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd98686241c8739099dc0cd87cd428b9d') in 0.014452 seconds 
========================================================================
Received Import Request.
  Time since last request: 26.600952 seconds.
  path: Assets/_MyGame/Bundles/Map/Map11.prefab
  artifactKey: Guid(5b753a5a20778624e9b05a67e11588c5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Bundles/Map/Map11.prefab using Guid(5b753a5a20778624e9b05a67e11588c5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'eea18f035719252ad5d9360beb5e96d7') in 0.004902 seconds 
========================================================================
Received Import Request.
  Time since last request: 65.572086 seconds.
  path: Assets/_MyGame/Bundles/Map/Map11.prefab
  artifactKey: Guid(5b753a5a20778624e9b05a67e11588c5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Bundles/Map/Map11.prefab using Guid(5b753a5a20778624e9b05a67e11588c5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '230018d722bf99739bac446d7becccbc') in 0.005955 seconds 
========================================================================
Received Import Request.
  Time since last request: 3.967322 seconds.
  path: Assets/_MyGame/Bundles/Map/Map11.prefab
  artifactKey: Guid(5b753a5a20778624e9b05a67e11588c5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Bundles/Map/Map11.prefab using Guid(5b753a5a20778624e9b05a67e11588c5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6249fa9c25d408005bb4f7de8f418322') in 0.005793 seconds 
========================================================================
Received Import Request.
  Time since last request: 1.872240 seconds.
  path: Assets/_MyGame/Bundles/Map/Map11.prefab
  artifactKey: Guid(5b753a5a20778624e9b05a67e11588c5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Bundles/Map/Map11.prefab using Guid(5b753a5a20778624e9b05a67e11588c5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '2f23f5b6e6ae9b2d99ffbe11417c957a') in 0.006055 seconds 
========================================================================
Received Import Request.
  Time since last request: 150.742130 seconds.
  path: Assets/_MyGame/RawRes/Map/11/14.png
  artifactKey: Guid(3eba681305c4e5b4bbc5698707ea43fe) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/RawRes/Map/11/14.png using Guid(3eba681305c4e5b4bbc5698707ea43fe) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'bcc52c89e2f376fc653e2e93bb020c65') in 0.031584 seconds 
========================================================================
Received Import Request.
  Time since last request: 660.030210 seconds.
  path: Assets/_MyGame/RawRes/Map/11/bg.png
  artifactKey: Guid(e9bb19c17de47a1459ba72efa17c9a7c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/RawRes/Map/11/bg.png using Guid(e9bb19c17de47a1459ba72efa17c9a7c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e8207350c5b9ad211cd21c54a938ef76') in 0.122836 seconds 
========================================================================
Received Import Request.
  Time since last request: 3.180442 seconds.
  path: Assets/_MyGame/RawRes/Map/11/wharf_2.png
  artifactKey: Guid(0b5ffa0e9958d454aa2898f24554677a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/RawRes/Map/11/wharf_2.png using Guid(0b5ffa0e9958d454aa2898f24554677a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c7a8749e1579607137af876b97804d93') in 0.014669 seconds 
========================================================================
Received Import Request.
  Time since last request: 422.940076 seconds.
  path: Assets/_MyGame/RawRes/Map/11/whatf_4.png
  artifactKey: Guid(a3e541810cf86cc4b99bafcd284c48c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/RawRes/Map/11/whatf_4.png using Guid(a3e541810cf86cc4b99bafcd284c48c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'aa0be69afb4e6a446b448a97fe3c8907') in 0.014640 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.002242 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.62 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.693 seconds
Domain Reload Profiling:
	ReloadAssembly (693ms)
		BeginReloadAssembly (84ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (29ms)
		EndReloadAssembly (558ms)
			LoadAssemblies (48ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (155ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (35ms)
			SetupLoadedEditorAssemblies (266ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (51ms)
				ProcessInitializeOnLoadAttributes (146ms)
				ProcessInitializeOnLoadMethodAttributes (54ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.23 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5615 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6267.
Memory consumption went from 220.8 MB to 219.5 MB.
Total: 4.254000 ms (FindLiveObjects: 0.227700 ms CreateObjectMapping: 0.084200 ms MarkObjects: 3.604500 ms  DeleteObjects: 0.337000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
